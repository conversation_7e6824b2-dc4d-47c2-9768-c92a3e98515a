#!/usr/bin/env python3
"""
Multi-Agent Authentication Demo App

A FastAPI demo showing our multi-agent authentication system in the correct repository.

Author: Jenkins Reader Agent Team
Version: 1.0.0
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from typing import Dict, List
import uvicorn
import sys
from pathlib import Path

# Ensure we're using the correct repository path
repo_root = Path("/home/<USER>/Truxt/adk-analyst")
sys.path.insert(0, str(repo_root))

# Import our multi-agent authentication system
from auth.models import (
    AgentType, RoleType, PermissionType, User, AgentRegistration,
    get_role_permissions, get_agent_default_permissions, create_default_roles
)
from auth.agent_manager import AgentAuthenticationService, AgentOrchestrationService

app = FastAPI(
    title="Multi-Agent Authentication Demo",
    description="Demo of enterprise multi-agent authentication system in correct repository",
    version="1.0.0"
)

# Initialize services
agent_auth_service = AgentAuthenticationService()
agent_orchestration_service = AgentOrchestrationService()

# Register demo agents
demo_agents = [
    {
        "agent_type": AgentType.JENKINS_AGENT,
        "name": "Jenkins Reader Agent",
        "description": "Read-only Jenkins data extraction agent",
        "version": "1.0.0",
        "capabilities": ["read_jobs", "read_builds", "read_pipelines"]
    },
    {
        "agent_type": AgentType.GITHUB_AGENT,
        "name": "GitHub Analytics Agent",
        "description": "Read-only GitHub repository analysis agent",
        "version": "1.0.0",
        "capabilities": ["read_repos", "read_commits", "read_prs"]
    },
    {
        "agent_type": AgentType.DORA_METRICS_AGENT,
        "name": "DORA Metrics Analyzer",
        "description": "DevOps Research and Assessment metrics analysis agent",
        "version": "1.0.0",
        "capabilities": ["calculate_dora", "deployment_frequency", "lead_time"]
    },
    {
        "agent_type": AgentType.SALES_ANALYST_AGENT,
        "name": "Sales Analytics Agent",
        "description": "Sales performance and pipeline analysis agent",
        "version": "1.0.0",
        "capabilities": ["sales_analysis", "pipeline_forecasting", "revenue_metrics"]
    }
]

for agent_config in demo_agents:
    agent_auth_service.register_agent(
        agent_type=agent_config["agent_type"],
        name=agent_config["name"],
        description=agent_config["description"],
        version=agent_config["version"],
        capabilities=agent_config["capabilities"],
        created_by="system"
    )

@app.get("/", response_class=HTMLResponse)
async def home():
    """Demo home page."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Multi-Agent Authentication Demo</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }}
            .section {{ margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
            .grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
            .agent {{ background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff; }}
            .role {{ background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #28a745; }}
            .status {{ color: #28a745; font-weight: bold; }}
            .read-only {{ color: #dc3545; font-weight: bold; }}
            .endpoint {{ margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }}
            .method {{ background: #007bff; color: white; padding: 5px 10px; border-radius: 3px; font-weight: bold; margin-right: 10px; }}
            .test-button {{ background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }}
            .test-button:hover {{ background: #0056b3; }}
            .repo-path {{ background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; color: #495057; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Multi-Agent Authentication System</h1>
                <p class="status">✅ Status: OPERATIONAL</p>
                <p>Enterprise-grade multi-agent platform with read-only SAAS agents</p>
                <div class="repo-path">📍 Repository: {repo_root}</div>
            </div>

            <div class="grid">
                <div class="section">
                    <h2>🏗️ Architecture Overview</h2>
                    <div class="agent">
                        <strong>Manager/Orchestrator Agent (1)</strong><br>
                        Top-level coordination and workflow management
                    </div>
                    <div class="agent">
                        <strong>Analysis Agents (5)</strong><br>
                        DORA Metrics, Sales, Marketing, Security, Performance
                    </div>
                    <div class="agent">
                        <strong>SAAS Agents (8)</strong><br>
                        <span class="read-only">READ-ONLY:</span> Jenkins, GitHub, JFrog, Slack, JIRA, Confluence, Salesforce, HubSpot
                    </div>
                </div>

                <div class="section">
                    <h2>👥 Role Hierarchy</h2>
                    <div class="role">
                        <strong>SUPER_ADMIN</strong> - Full system access (38 permissions)
                    </div>
                    <div class="role">
                        <strong>AGENT_ORCHESTRATOR</strong> - Agent coordination (25 permissions)
                    </div>
                    <div class="role">
                        <strong>SENIOR_ANALYST</strong> - Full analysis capabilities (21 permissions)
                    </div>
                    <div class="role">
                        <strong>ANALYST</strong> - Domain-specific analysis (8 permissions)
                    </div>
                    <div class="role">
                        <strong>VIEWER</strong> - Read-only access (2 permissions)
                    </div>
                    <div class="role">
                        <strong>AGENT_SERVICE</strong> - Inter-agent communication (16 permissions)
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🧪 Test Endpoints</h2>
                
                <div class="endpoint">
                    <span class="method">GET</span> <strong>/agents</strong>
                    <p>List all registered agents</p>
                    <button class="test-button" onclick="testEndpoint('/agents')">Test</button>
                </div>

                <div class="endpoint">
                    <span class="method">GET</span> <strong>/roles</strong>
                    <p>List all available roles and permissions</p>
                    <button class="test-button" onclick="testEndpoint('/roles')">Test</button>
                </div>

                <div class="endpoint">
                    <span class="method">GET</span> <strong>/security-check</strong>
                    <p>Verify SAAS agents have read-only permissions</p>
                    <button class="test-button" onclick="testEndpoint('/security-check')">Test Security</button>
                </div>

                <div class="endpoint">
                    <span class="method">GET</span> <strong>/orchestrate-demo</strong>
                    <p>Demo agent orchestration workflow</p>
                    <button class="test-button" onclick="testEndpoint('/orchestrate-demo')">Test Orchestration</button>
                </div>
            </div>

            <div class="section">
                <h2>📊 Test Results</h2>
                <div id="results" style="background: #f8f9fa; padding: 20px; border-radius: 5px; min-height: 100px;">
                    <p>Click test buttons above to see results...</p>
                </div>
            </div>
        </div>

        <script>
            async function testEndpoint(url) {{
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML = '<p>Testing ' + url + '...</p>';
                
                try {{
                    const response = await fetch(url);
                    const data = await response.json();
                    resultsDiv.innerHTML = '<h4>✅ ' + url + '</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }} catch (error) {{
                    resultsDiv.innerHTML = '<h4>❌ ' + url + '</h4><p>Error: ' + error.message + '</p>';
                }}
            }}
        </script>
    </body>
    </html>
    """

@app.get("/agents")
async def list_agents():
    """List all registered agents."""
    agents = agent_auth_service.get_all_agents()
    return {
        "status": "success",
        "repository": str(repo_root),
        "data": {
            "total_agents": len(agents),
            "agents": [
                {
                    "agent_id": agent.agent_id,
                    "agent_type": agent.agent_type,
                    "name": agent.name,
                    "description": agent.description,
                    "version": agent.version,
                    "capabilities": agent.capabilities,
                    "is_active": agent.is_active,
                    "api_key_preview": agent.api_key[:20] + "..." if agent.api_key else None
                }
                for agent in agents
            ]
        }
    }

@app.get("/roles")
async def list_roles():
    """List all available roles."""
    roles = create_default_roles()
    return {
        "status": "success",
        "repository": str(repo_root),
        "data": {
            "total_roles": len(roles),
            "roles": [
                {
                    "role": role.name.value if hasattr(role.name, 'value') else role.name,
                    "description": role.description,
                    "permission_count": len(role.permissions),
                    "permissions": [perm.value for perm in role.permissions]
                }
                for role in roles
            ]
        }
    }

@app.get("/security-check")
async def security_check():
    """Verify SAAS agents have read-only permissions."""
    saas_agents = [
        AgentType.JENKINS_AGENT, AgentType.GITHUB_AGENT, AgentType.JFROG_AGENT,
        AgentType.SLACK_AGENT, AgentType.JIRA_AGENT, AgentType.CONFLUENCE_AGENT,
        AgentType.SALESFORCE_AGENT, AgentType.HUBSPOT_AGENT
    ]
    
    security_results = []
    all_secure = True
    
    for agent_type in saas_agents:
        permissions = get_agent_default_permissions(agent_type)
        
        # Check for dangerous permissions
        dangerous_permissions = {
            PermissionType.SYSTEM_ADMIN,
            PermissionType.PLATFORM_MANAGE,
        }
        
        has_dangerous = any(perm in permissions for perm in dangerous_permissions)
        is_secure = not has_dangerous
        
        if not is_secure:
            all_secure = False
        
        security_results.append({
            "agent_type": agent_type.value,
            "is_read_only": is_secure,
            "permission_count": len(permissions),
            "permissions": [perm.value for perm in permissions],
            "dangerous_permissions": [perm.value for perm in permissions if perm in dangerous_permissions]
        })
    
    return {
        "status": "success",
        "repository": str(repo_root),
        "data": {
            "all_saas_agents_secure": all_secure,
            "total_saas_agents": len(saas_agents),
            "security_results": security_results,
            "summary": "All SAAS agents have read-only permissions" if all_secure else "SECURITY ISSUE: Some SAAS agents have write permissions"
        }
    }

@app.get("/orchestrate-demo")
async def orchestrate_demo():
    """Demo agent orchestration workflow."""
    try:
        # Test orchestrating a DORA metrics analysis
        communications = agent_orchestration_service.orchestrate_analysis(
            analysis_type="dora_metrics",
            data_sources=[AgentType.JENKINS_AGENT, AgentType.GITHUB_AGENT],
            target_agents=[AgentType.DORA_METRICS_AGENT]
        )
        
        return {
            "status": "success",
            "repository": str(repo_root),
            "data": {
                "analysis_type": "dora_metrics",
                "communications_sent": len(communications),
                "communications": [
                    {
                        "id": comm.communication_id,
                        "source": comm.source_agent,
                        "target": comm.target_agent,
                        "message_type": comm.message_type,
                        "status": comm.status,
                        "created_at": comm.created_at.isoformat()
                    }
                    for comm in communications.values()
                ]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

if __name__ == "__main__":
    print("🚀 Starting Multi-Agent Authentication Demo")
    print(f"📍 Repository: {repo_root}")
    print("🌐 URL: http://localhost:8080")
    print("📚 API Docs: http://localhost:8080/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
