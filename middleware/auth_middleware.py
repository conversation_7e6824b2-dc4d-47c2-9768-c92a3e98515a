"""
Authentication Middleware

This module provides FastAPI middleware for handling authentication
and user context injection for the Jenkins Reader Agent.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Optional, Callable
from fastapi import Request, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import logging

from auth.authentication import AuthenticationService, TokenValidator
from auth.session_manager import Session<PERSON>anager
from auth.models import User
from auth.exceptions import (
    AuthenticationError, TokenExpiredError, InvalidTokenError,
    SessionExpiredError
)

logger = logging.getLogger(__name__)

# Global instances (to be initialized by the application)
_auth_service: Optional[AuthenticationService] = None
_session_manager: Optional[SessionManager] = None
_token_validator: Optional[TokenValidator] = None

# Security scheme for FastAPI
security = HTTPBearer(auto_error=False)


def init_auth_middleware(
    auth_service: AuthenticationService,
    session_manager: <PERSON><PERSON><PERSON><PERSON>,
    token_validator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
):
    """Initialize authentication middleware with required services."""
    global _auth_service, _session_manager, _token_validator
    _auth_service = auth_service
    _session_manager = session_manager
    _token_validator = token_validator


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Middleware for handling authentication and user context."""
    
    def __init__(
        self,
        app,
        auth_service: AuthenticationService,
        session_manager: SessionManager,
        excluded_paths: Optional[list] = None
    ):
        super().__init__(app)
        self.auth_service = auth_service
        self.session_manager = session_manager
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/health",
            "/auth/login",
            "/auth/callback",
            "/auth/logout",
            "/static"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through authentication middleware."""
        # Skip authentication for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # Extract user context
        user = None
        session_id = None
        
        try:
            # Try to get user from token
            authorization = request.headers.get("Authorization")
            if authorization:
                user = await self._authenticate_with_token(authorization, request)
            
            # Try to get user from session
            if not user:
                session_id = request.cookies.get("session_id")
                if session_id:
                    user = await self._authenticate_with_session(session_id, request)
            
            # Add user context to request state
            request.state.user = user
            request.state.session_id = session_id
            
        except (AuthenticationError, TokenExpiredError, InvalidTokenError) as e:
            logger.warning(f"Authentication failed: {e}")
            # For API endpoints, return 401
            if request.url.path.startswith("/api/"):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=str(e),
                    headers={"WWW-Authenticate": "Bearer"}
                )
            # For web endpoints, continue without user (will be handled by route)
            request.state.user = None
            request.state.session_id = None
        
        except Exception as e:
            logger.error(f"Unexpected authentication error: {e}")
            request.state.user = None
            request.state.session_id = None
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        return response
    
    async def _authenticate_with_token(self, authorization: str, request: Request) -> User:
        """Authenticate user with JWT token."""
        try:
            token = self._extract_token_from_header(authorization)
            user = await self.auth_service.validate_token(token)
            
            # Log successful authentication
            logger.info(f"User authenticated via token: {user.email}")
            
            return user
        except Exception as e:
            logger.warning(f"Token authentication failed: {e}")
            raise
    
    async def _authenticate_with_session(self, session_id: str, request: Request) -> User:
        """Authenticate user with session."""
        try:
            # Get client info
            ip_address = self._get_client_ip(request)
            user_agent = request.headers.get("User-Agent")
            
            # Validate session
            secure_session = await self.session_manager.validate_session(
                session_id, ip_address, user_agent
            )
            
            # Get user from session
            user_id = secure_session.session_data.user_id
            # Note: In a real implementation, you'd fetch the user from a user store
            # For now, we'll need to integrate with the auth service's user store
            
            logger.info(f"User authenticated via session: {user_id}")
            
            # Return a placeholder user (implement proper user fetching)
            return None  # TODO: Implement user fetching from session
            
        except SessionExpiredError:
            logger.warning(f"Session expired: {session_id}")
            raise AuthenticationError("Session expired")
        except Exception as e:
            logger.warning(f"Session authentication failed: {e}")
            raise
    
    def _extract_token_from_header(self, authorization: str) -> str:
        """Extract token from Authorization header."""
        if not authorization:
            raise InvalidTokenError("Missing authorization header")
        
        try:
            scheme, token = authorization.split()
            if scheme.lower() != 'bearer':
                raise InvalidTokenError("Invalid authorization scheme")
            return token
        except ValueError:
            raise InvalidTokenError("Invalid authorization header format")
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"


# Dependency functions for FastAPI routes
async def get_current_user(request: Request) -> User:
    """Get current authenticated user (required)."""
    user = getattr(request.state, 'user', None)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user


async def get_current_user_optional(request: Request) -> Optional[User]:
    """Get current authenticated user (optional)."""
    return getattr(request.state, 'user', None)


async def get_session_id(request: Request) -> Optional[str]:
    """Get current session ID."""
    return getattr(request.state, 'session_id', None)


# Token-based authentication dependency
async def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> User:
    """Get current user from JWT token."""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    if not _token_validator:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service not initialized"
        )
    
    try:
        claims = _token_validator.validate_access_token(credentials.credentials)
        
        # Get user from auth service
        if not _auth_service:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service not initialized"
            )
        
        user = await _auth_service.validate_token(credentials.credentials)
        return user
        
    except TokenExpiredError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except InvalidTokenError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"}
        )


# Decorator for requiring authentication
def require_auth(func):
    """Decorator to require authentication for a route."""
    async def wrapper(*args, **kwargs):
        # This would be used with FastAPI dependencies
        return await func(*args, **kwargs)
    return wrapper


# Role-based authentication dependencies
async def require_admin_user(current_user: User = Depends(get_current_user_from_token)) -> User:
    """Require admin user."""
    from auth.authorization import AuthorizationService
    from auth.models import PermissionType

    auth_service = AuthorizationService()
    try:
        auth_service.authorize_user(current_user, PermissionType.SYSTEM_ADMIN)
        return current_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )


async def require_analyst_user(current_user: User = Depends(get_current_user_from_token)) -> User:
    """Require analyst user or higher."""
    from auth.authorization import AuthorizationService
    from auth.models import PermissionType

    auth_service = AuthorizationService()
    try:
        # Check for any analysis permission
        analysis_permissions = [
            PermissionType.DORA_ANALYZE,
            PermissionType.SALES_ANALYZE,
            PermissionType.MARKETING_ANALYZE,
            PermissionType.SECURITY_ANALYZE,
            PermissionType.PERFORMANCE_ANALYZE,
        ]

        # User needs at least one analysis permission
        user_permissions = auth_service.get_user_permissions(current_user)
        if not any(perm in user_permissions for perm in analysis_permissions):
            raise Exception("No analysis permissions")

        return current_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Analyst access required"
        )


async def require_jenkins_access(current_user: User = Depends(get_current_user_from_token)) -> User:
    """Require Jenkins read access."""
    from auth.authorization import AuthorizationService
    from auth.models import PermissionType

    auth_service = AuthorizationService()
    try:
        auth_service.authorize_user(current_user, PermissionType.JENKINS_READ)
        return current_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Jenkins access required"
        )


async def require_agent_orchestrator(current_user: User = Depends(get_current_user_from_token)) -> User:
    """Require agent orchestrator permissions."""
    from auth.authorization import AuthorizationService
    from auth.models import PermissionType

    auth_service = AuthorizationService()
    try:
        auth_service.authorize_user(current_user, PermissionType.AGENT_ORCHESTRATE)
        return current_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Agent orchestrator access required"
        )


async def require_agent_manager(current_user: User = Depends(get_current_user_from_token)) -> User:
    """Require agent manager permissions."""
    from auth.authorization import AuthorizationService
    from auth.models import PermissionType

    auth_service = AuthorizationService()
    try:
        auth_service.authorize_user(current_user, PermissionType.AGENT_MANAGE)
        return current_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Agent manager access required"
        )
