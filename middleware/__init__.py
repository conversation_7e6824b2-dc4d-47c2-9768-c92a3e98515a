"""
Middleware Module

This module provides FastAPI middleware components for the Jenkins Reader Agent,
including authentication, authorization, rate limiting, and security headers.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from .auth_middleware import (
    AuthenticationMiddleware,
    get_current_user,
    get_current_user_optional,
    require_auth,
    init_auth_middleware,
    get_current_user_from_token,
    require_admin_user,
    require_analyst_user,
    require_jenkins_access,
)
from .rate_limiting import (
    RateLimitingMiddleware,
    RateLimiter,
    rate_limit,
)

__all__ = [
    "AuthenticationMiddleware",
    "get_current_user",
    "get_current_user_optional", 
    "require_auth",
    "init_auth_middleware",
    "get_current_user_from_token",
    "require_admin_user",
    "require_analyst_user", 
    "require_jenkins_access",
    "RateLimitingMiddleware",
    "RateLimiter",
    "rate_limit",
]
