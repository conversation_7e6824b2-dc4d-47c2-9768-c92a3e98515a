#!/usr/bin/env python3
"""
Multi-Agent Authentication System Unit Tests

This script tests the core functionality of our multi-agent authentication system.

Author: Jenkins Reader Agent Team
Version: 1.0.0
"""

import sys
from pathlib import Path
import asyncio
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from auth import (
    AuthenticationService, OAuth2Provider, JWTManager,
    AgentAuthenticationService, AgentOrchestrationService,
    User, AgentType, RoleType, PermissionType,
    create_default_roles, get_agent_default_permissions
)
from auth.authorization import AuthorizationService, Per<PERSON><PERSON><PERSON><PERSON>


def test_role_permissions():
    """Test role and permission system."""
    print("🔐 Testing Role and Permission System...")
    
    # Test role creation
    roles = create_default_roles()
    print(f"✅ Created {len(roles)} default roles")
    
    # Test permission checking
    test_user = User(
        email="<EMAIL>",
        name="Test User",
        role=RoleType.SENIOR_ANALYST,
        accessible_agents=[AgentType.JENKINS_AGENT, AgentType.GITHUB_AGENT]
    )
    
    # Test permissions
    assert test_user.has_permission(PermissionType.JENKINS_READ)
    assert test_user.has_permission(PermissionType.DORA_ANALYZE)
    assert not test_user.has_permission(PermissionType.SYSTEM_ADMIN)
    print("✅ Permission checking works correctly")
    
    # Test agent access
    assert test_user.can_access_agent(AgentType.JENKINS_AGENT)
    assert test_user.can_access_agent(AgentType.GITHUB_AGENT)
    assert not test_user.can_manage_agent(AgentType.JENKINS_AGENT)
    print("✅ Agent access control works correctly")


def test_jwt_tokens():
    """Test JWT token generation and validation."""
    print("🔑 Testing JWT Token System...")
    
    jwt_manager = JWTManager(
        secret_key="test_secret_key",
        algorithm="HS256",
        access_token_expire_minutes=30
    )
    
    test_user = User(
        email="<EMAIL>",
        name="Test User",
        role=RoleType.ANALYST
    )
    
    # Generate token
    token = jwt_manager.create_access_token(test_user)
    print(f"✅ Generated JWT token: {token.access_token[:50]}...")
    
    # Validate token
    claims = jwt_manager.verify_token(token.access_token)
    assert claims.email == test_user.email
    assert claims.role == test_user.role
    print("✅ JWT token validation works correctly")


def test_agent_authentication():
    """Test agent authentication system."""
    print("🤖 Testing Agent Authentication System...")
    
    agent_auth = AgentAuthenticationService()
    
    # Register a test agent
    registration = agent_auth.register_agent(
        agent_type=AgentType.JENKINS_AGENT,
        name="Test Jenkins Agent",
        description="Test agent for unit testing",
        version="1.0.0",
        capabilities=["read_jobs", "read_builds"],
        created_by="test_user"
    )
    
    print(f"✅ Registered agent: {registration.name}")
    print(f"   Agent ID: {registration.agent_id}")
    print(f"   API Key: {registration.api_key[:20]}...")
    
    # Test agent authentication
    agent_user = agent_auth.authenticate_agent(registration.api_key)
    assert agent_user is not None
    assert agent_user.agent_type == AgentType.JENKINS_AGENT
    assert agent_user.is_service_account
    print("✅ Agent authentication works correctly")
    
    # Test agent permissions
    agent_permissions = get_agent_default_permissions(AgentType.JENKINS_AGENT)
    assert PermissionType.JENKINS_READ in agent_permissions
    assert PermissionType.DATA_READ in agent_permissions
    assert PermissionType.AGENT_COMMUNICATION in agent_permissions
    print("✅ Agent permissions assigned correctly")


def test_agent_orchestration():
    """Test agent orchestration system."""
    print("🎭 Testing Agent Orchestration System...")
    
    orchestrator = AgentOrchestrationService()
    
    # Test orchestrating a DORA metrics analysis
    communications = orchestrator.orchestrate_analysis(
        analysis_type="dora_metrics",
        data_sources=[AgentType.JENKINS_AGENT, AgentType.GITHUB_AGENT],
        target_agents=[AgentType.DORA_METRICS_AGENT]
    )
    
    print(f"✅ Orchestrated analysis with {len(communications)} communications")
    
    # Verify communications
    for comm_key, comm in communications.items():
        print(f"   {comm_key}: {comm.source_agent} → {comm.target_agent}")
        assert comm.status == "sent"
    
    print("✅ Agent orchestration works correctly")


def test_authorization_service():
    """Test authorization service."""
    print("🛡️ Testing Authorization Service...")
    
    auth_service = AuthorizationService()
    
    # Test different user roles
    admin_user = User(
        email="<EMAIL>",
        name="Admin User",
        role=RoleType.SUPER_ADMIN
    )
    
    analyst_user = User(
        email="<EMAIL>",
        name="Analyst User",
        role=RoleType.ANALYST
    )
    
    viewer_user = User(
        email="<EMAIL>",
        name="Viewer User",
        role=RoleType.VIEWER
    )
    
    # Test admin permissions
    assert auth_service.authorize_user(admin_user, PermissionType.SYSTEM_ADMIN)
    assert auth_service.authorize_user(admin_user, PermissionType.JENKINS_READ)
    print("✅ Admin authorization works correctly")
    
    # Test analyst permissions
    assert auth_service.authorize_user(analyst_user, PermissionType.JENKINS_READ)
    assert auth_service.authorize_user(analyst_user, PermissionType.DORA_ANALYZE)
    print("✅ Analyst authorization works correctly")
    
    # Test viewer permissions
    assert auth_service.authorize_user(viewer_user, PermissionType.DASHBOARD_VIEW)
    try:
        auth_service.authorize_user(viewer_user, PermissionType.SYSTEM_ADMIN)
        assert False, "Viewer should not have admin permissions"
    except Exception:
        pass  # Expected
    print("✅ Viewer authorization works correctly")


def test_saas_agent_read_only():
    """Test that SAAS agents have read-only permissions."""
    print("📖 Testing SAAS Agent Read-Only Permissions...")
    
    saas_agents = [
        AgentType.JENKINS_AGENT,
        AgentType.GITHUB_AGENT,
        AgentType.JFROG_AGENT,
        AgentType.SLACK_AGENT,
        AgentType.JIRA_AGENT,
        AgentType.CONFLUENCE_AGENT,
        AgentType.SALESFORCE_AGENT,
        AgentType.HUBSPOT_AGENT,
    ]
    
    for agent_type in saas_agents:
        permissions = get_agent_default_permissions(agent_type)
        
        # Verify read-only permissions
        read_permission = f"{agent_type.value.replace('_agent', '').upper()}_READ"
        if hasattr(PermissionType, read_permission):
            expected_perm = getattr(PermissionType, read_permission)
            assert expected_perm in permissions, f"{agent_type} missing read permission"
        
        # Verify no write/admin permissions
        write_permissions = [
            PermissionType.SYSTEM_ADMIN,
            PermissionType.SYSTEM_CONFIG,
            PermissionType.AGENT_DEPLOY,
            PermissionType.AGENT_CONFIGURE,
            PermissionType.USER_WRITE,
            PermissionType.USER_DELETE,
        ]
        
        for write_perm in write_permissions:
            assert write_perm not in permissions, f"{agent_type} has write permission: {write_perm}"
        
        print(f"✅ {agent_type.value} has read-only permissions")


def run_all_tests():
    """Run all tests."""
    print("🧪 Starting Multi-Agent Authentication System Tests")
    print("=" * 60)
    
    try:
        test_role_permissions()
        print()
        
        test_jwt_tokens()
        print()
        
        test_agent_authentication()
        print()
        
        test_agent_orchestration()
        print()
        
        test_authorization_service()
        print()
        
        test_saas_agent_read_only()
        print()
        
        print("=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Multi-Agent Authentication System is working correctly")
        print()
        print("🔐 Security Features Verified:")
        print("   • Role-based access control (RBAC)")
        print("   • JWT token generation and validation")
        print("   • Agent authentication with API keys")
        print("   • Agent orchestration workflows")
        print("   • Read-only permissions for SAAS agents")
        print("   • Permission-based authorization")
        print()
        print("🚀 Ready for production deployment!")
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    run_all_tests()
